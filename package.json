{"name": "<PERSON><PERSON><PERSON>aodian-beginner-guidance", "version": "0.1.1", "private": true, "scripts": {"serve": "rsbuild dev --mode development --env-mode development", "build:test": "rsbuild build --mode production --env-mode test", "build:dev": "rsbuild build --mode production --env-mode development", "build": "rsbuild build --mode production --env-mode production", "lint": "vue-cli-service lint", "format": "prettier --write \"src/**/*.ts\" \"src/**/*.ts\" \"src/**/*.vue\" \"src/**/*.less\""}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@simplex/simple-mcprotocol": "2.3.3", "@simplex/simple-oort": "4.3.7", "normalize.css": "^8.0.1", "pinia": "^2.1.7", "register-service-worker": "^1.7.2", "vue": "^3.2.13"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.13", "@babel/plugin-proposal-object-rest-spread": "^7.18.9", "@babel/plugin-proposal-optional-chaining": "^7.18.9", "@paas/eslint-config-paas": "^2.0.5", "@rsbuild/core": "^1.4.15", "@rsbuild/plugin-babel": "^1.0.6", "@rsbuild/plugin-less": "^1.4.0", "@rsbuild/plugin-vue": "^1.1.1", "@rsbuild/plugin-vue-jsx": "^1.1.1", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/cli-plugin-pwa": "^5.0.8", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "autoprefixer": "^10.4.17", "eruda": "^2.5.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "husky": "^4.2.5", "ip": "^2.0.1", "less": "^4.0.0", "less-loader": "^6.0.0", "postcss": "^8.4.14", "postcss-px-to-viewport-8-plugin": "^1.2.5", "prettier": "^2.4.1", "tailwindcss": "^3.4.1", "typescript": "~4.9.5", "unplugin-vue-components": "^0.21.2", "vconsole-webpack-plugin": "^1.8.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended", "@vue/typescript/recommended", "@paas/eslint-config-paas"], "parserOptions": {"sourceType": "module"}, "plugins": ["prettier"], "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}