import path from 'path';
import { defineConfig } from '@rsbuild/core';
import { pluginVue } from '@rsbuild/plugin-vue';
import { pluginBabel } from '@rsbuild/plugin-babel';
import { pluginVueJsx } from '@rsbuild/plugin-vue-jsx';
import { pluginLess } from '@rsbuild/plugin-less';
import getAppConfig from './app-env/config.js';
import pageJson from './page.json';

const rootPath = process.cwd();

const isProd = JSON.parse(process.env.isProd as string);
const isDev = JSON.parse(process.env.isDev as string);
const isLocal = JSON.parse(process.env.isLocal as string);
const isTest = JSON.parse(process.env.isTest as string);

console.log('isProd, isDev, isLocal, isTest', isProd, isDev, isLocal, isTest);

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);

const makePublicAssetsPath = path => (isLocal ? path : `./${path}`);

export default defineConfig({
  plugins: [
    pluginBabel({
      include: /\.(?:jsx|tsx)$/
    }),
    pluginVue(),
    pluginVueJsx(),
    pluginLess()
  ],
  source: {
    entry: pageJson,
    include: [
      {
        and: [rootPath, { not: /[\\/]node_modules[\\/]/ }]
      },
      /\.(?:ts|tsx|jsx|mts|cts)$/
    ],
    define: {
      APP: JSON.stringify(appConfig)
    }
  },
  server: {
    port: 5550
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  output: {
    sourceMap: isLocal || isDev,
    legalComments: 'none',
    polyfill: 'entry',
    assetPrefix: '/activity.kakamobi.com/jiakaobaodian-beginner-guidance/'
  },
  html: {
    template({ entryName }) {
      return './public/index.html';
    },

    title({ entryName }) {
      return pageJson[entryName]?.title || '帮我找驾校';
    },
    templateParameters({ entryName }) {
      const parameters: any = {};

      const vConsoleScript = !isProd ? makePublicAssetsPath(`vconsole/vconsole.min.js`) : ' ';

      parameters.vConsoleScript = vConsoleScript;

      return parameters;
    }
  }
});