import path from 'path';
import { defineConfig } from '@rsbuild/core';
import { pluginVue } from '@rsbuild/plugin-vue';
import { pluginBabel } from '@rsbuild/plugin-babel';
import { pluginVueJsx } from '@rsbuild/plugin-vue-jsx';
import { pluginLess } from '@rsbuild/plugin-less';
import Components from 'unplugin-vue-components/webpack';
import { VantResolver } from 'unplugin-vue-components/resolvers';
import ip from 'ip';
import getAppConfig from './app-env/config.js';
import pageJson from './page.json';

const rootPath = process.cwd();

const isProd = JSON.parse(process.env.isProd as string);
const isDev = JSON.parse(process.env.isDev as string);
const isLocal = JSON.parse(process.env.isLocal as string);
const isTest = JSON.parse(process.env.isTest as string);

console.log('isProd, isDev, isLocal, isTest', isProd, isDev, isLocal, isTest);

const appConfig = getAppConfig(isProd, isDev, isLocal, isTest);

const makePublicAssetsPath = path => (isLocal ? path : `./${path}`);

export default defineConfig({
  plugins: [
    pluginBabel({
      include: /\.(?:jsx|tsx)$/
    }),
    pluginVue(),
    pluginVueJsx(),
    pluginLess({
      lessLoaderOptions: {
        lessOptions: {
          javascriptEnabled: true
        }
      }
    })
  ],
  tools: {
    rspack: {
      plugins: [
        // Vant组件按需引入配置
        Components({
          resolvers: [VantResolver()]
        })
      ]
    },
    swc: isProd ? {
      jsc: {
        minify: {
          compress: {
            drop_console: true,
            drop_debugger: true
          }
        }
      }
    } : undefined
  },
  source: {
    entry: pageJson,
    include: [
      {
        and: [rootPath, { not: /[\\/]node_modules[\\/]/ }]
      },
      /\.(?:ts|tsx|jsx|mts|cts)$/
    ],
    define: {
      APP: JSON.stringify(appConfig)
    }
  },
  server: {
    port: 5550,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    client: {
      overlay: false,
      webSocketURL: `wss://${ip.address().replace(/\./g, '-')}-5550.local.mucang.cn/ws`
    }
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src')
    }
  },
  output: {
    sourceMap: isLocal || isDev,
    legalComments: 'none',
    polyfill: 'entry',
    assetPrefix: isLocal ? '/' : '/activity.kakamobi.com/jiakaobaodian-beginner-guidance/',
    distPath: {
      root: 'dist'
    }
  },
  performance: {
    chunkSplit: {
      strategy: 'split-by-experience',
      override: {
        chunks: 'all',
        maxAsyncRequests: 10,
        maxInitialRequests: 50,
        minSize: 100000,
        cacheGroups: {
          libs: {
            name(module: any) {
              // 拆分每个 npm 包
              let packageName = 'null' + Math.floor(Math.random() * 10);
              const npmName = module.context?.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/);
              if (npmName && npmName[1]) {
                packageName = npmName[1];
              }
              return `npm.${packageName.replace('@', '')}`;
            },
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial'
          },
          appModule: {
            name(module: any) {
              let packageName = module.context?.match(/[\\/]application[\\/](.*?)([\\/]|$)/);
              if (packageName) {
                packageName = packageName[1];
              }
              return `application.${packageName}`;
            },
            test: /[\\/]src[\\/]_?application(.*)/,
            priority: 8,
            enforce: true
          }
        }
      }
    }
  },
  html: {
    template({ entryName }) {
      return './public/index.html';
    },

    title({ entryName }) {
      return pageJson[entryName]?.title || '帮我找驾校';
    },
    templateParameters({ entryName }) {
      const parameters: any = {};

      const vConsoleScript = !isProd ? makePublicAssetsPath(`vconsole/vconsole.min.js`) : ' ';

      parameters.vConsoleScript = vConsoleScript;

      return parameters;
    }
  }
});